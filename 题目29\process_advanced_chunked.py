import pandas as pd
import os
import gc
import psutil
from typing import List, Dict, Optional, Generator
import numpy as np
from datetime import datetime

# 修正文件路径为相对路径
file_path = os.path.join(os.path.dirname(__file__), '原油期权日行情历史数据20250721.xlsx')

class AdvancedChunkedExcelProcessor:
    """高级分块处理Excel数据的类，支持内存监控和自适应分块"""
    
    def __init__(self, file_path: str, chunk_size: int = 10000, max_memory_mb: int = 500):
        """
        初始化处理器
        
        Args:
            file_path: Excel文件路径
            chunk_size: 每个分块的行数，默认10000行
            max_memory_mb: 最大内存使用量（MB），超过时会自动调整chunk_size
        """
        self.file_path = file_path
        self.initial_chunk_size = chunk_size
        self.chunk_size = chunk_size
        self.max_memory_mb = max_memory_mb
        self.indicators = []
        self.col_map = {
            '成交金额': '成交额（万）',
            '成交量': '成交量',
            '持仓量': '持仓量',
            '行权量': '行权量',
        }
        self.process_stats = {
            'total_rows_processed': 0,
            'chunks_processed': 0,
            'memory_adjustments': 0,
            'start_time': None,
            'end_time': None
        }
        
    def get_memory_usage(self) -> float:
        """获取当前内存使用量（MB）"""
        process = psutil.Process(os.getpid())
        return process.memory_info().rss / 1024 / 1024
    
    def adjust_chunk_size_if_needed(self):
        """根据内存使用情况自动调整chunk_size"""
        current_memory = self.get_memory_usage()
        if current_memory > self.max_memory_mb:
            old_chunk_size = self.chunk_size
            self.chunk_size = max(1000, int(self.chunk_size * 0.7))  # 减少30%，最小1000行
            self.process_stats['memory_adjustments'] += 1
            print(f"内存使用过高 ({current_memory:.1f}MB > {self.max_memory_mb}MB)，调整chunk_size: {old_chunk_size} -> {self.chunk_size}")
            gc.collect()  # 强制垃圾回收
    
    def read_demand_indicators(self) -> List[str]:
        """读取需求表首行，获取指标列表"""
        print("正在读取需求指标...")
        demand_sheet = 0  # 第 1 个 sheet
        row0 = pd.read_excel(self.file_path, sheet_name=demand_sheet, nrows=1)
        
        # 收集"包含指标*"列
        indicators = [
            row0[c].strip()
            for c in row0.columns if c.startswith('包含指标') and pd.notna(row0[c])
        ]
        
        self.indicators = indicators
        print(f"找到指标: {indicators}")
        return indicators
    
    def get_sheet_info(self, sheet_name: str) -> Dict:
        """获取sheet的详细信息"""
        print(f"正在分析 {sheet_name} 的结构...")
        
        # 读取少量数据来分析结构
        sample_df = pd.read_excel(self.file_path, sheet_name=sheet_name, nrows=100)
        
        # 获取总行数（使用更高效的方法）
        temp_df = pd.read_excel(self.file_path, sheet_name=sheet_name, usecols=[0])
        total_rows = len(temp_df)
        del temp_df
        gc.collect()
        
        info = {
            'total_rows': total_rows,
            'columns': list(sample_df.columns),
            'dtypes': sample_df.dtypes.to_dict(),
            'memory_per_row_estimate': sample_df.memory_usage(deep=True).sum() / len(sample_df)
        }
        
        print(f"Sheet信息: 总行数={total_rows}, 列数={len(sample_df.columns)}, 预估每行内存={info['memory_per_row_estimate']:.2f}字节")
        return info
    
    def chunk_reader(self, sheet_name: str, usecols: List[str]) -> Generator[pd.DataFrame, None, None]:
        """生成器：逐块读取Excel数据"""
        sheet_info = self.get_sheet_info(sheet_name)
        total_rows = sheet_info['total_rows']
        
        # 根据内存估算调整初始chunk_size
        estimated_memory_per_chunk = (sheet_info['memory_per_row_estimate'] * self.chunk_size) / (1024 * 1024)  # MB
        if estimated_memory_per_chunk > self.max_memory_mb * 0.5:  # 如果预估超过最大内存的50%
            self.chunk_size = max(1000, int(self.chunk_size * 0.5))
            print(f"根据内存预估调整chunk_size为: {self.chunk_size}")
        
        current_row = 0
        chunk_num = 0
        
        while current_row < total_rows:
            chunk_num += 1
            nrows = min(self.chunk_size, total_rows - current_row)
            
            print(f"读取第 {chunk_num} 块: 行 {current_row+1} 到 {current_row+nrows} (内存: {self.get_memory_usage():.1f}MB)")
            
            try:
                chunk_df = pd.read_excel(
                    self.file_path,
                    sheet_name=sheet_name,
                    usecols=usecols,
                    skiprows=current_row if current_row > 0 else 0,
                    nrows=nrows,
                    parse_dates=['日期'] if '日期' in usecols else None
                )
                
                if not chunk_df.empty:
                    yield chunk_df
                    
                current_row += nrows
                self.adjust_chunk_size_if_needed()
                
            except Exception as e:
                print(f"读取第 {chunk_num} 块时出错: {e}")
                current_row += nrows  # 跳过这个块继续处理
                continue
    
    def process_chunk(self, chunk_df: pd.DataFrame) -> pd.DataFrame:
        """处理单个数据块"""
        try:
            # ── 如需：成交金额按"成交量(张) → 万"换算后再参与求和 ──
            if '成交金额' in self.indicators:
                chunk_df['成交金额_调整'] = chunk_df[self.col_map['成交金额']] * (chunk_df['日成交量(张)'] / 1e4)
                sum_cols = ['成交金额_调整'] + [
                    self.col_map[i] for i in self.indicators if i != '成交金额'
                ]
            else:
                sum_cols = [self.col_map[i] for i in self.indicators]
            
            # ── 逐日求和 ──
            chunk_df['指标和'] = chunk_df[sum_cols].sum(axis=1)
            
            # 返回处理后的结果
            result = chunk_df[['日期', '指标和']].copy()
            
            # 清理临时列
            if '成交金额_调整' in chunk_df.columns:
                chunk_df.drop('成交金额_调整', axis=1, inplace=True)
            
            return result
            
        except Exception as e:
            print(f"处理数据块时出错: {e}")
            return pd.DataFrame()
    
    def process_in_chunks(self) -> pd.DataFrame:
        """分块处理Excel数据"""
        self.process_stats['start_time'] = datetime.now()
        
        # 1. 读取需求指标
        self.read_demand_indicators()
        
        # 2. 准备读取参数
        data_sheet_name = '原油期权行情历史'  # 第 3 个 sheet 的名称
        usecols = ['日期'] + [self.col_map[i] for i in self.indicators] + ['日成交量(张)']
        
        print(f"开始分块处理，初始chunk_size={self.chunk_size}, 最大内存限制={self.max_memory_mb}MB")
        
        # 3. 分块处理数据
        result_chunks = []
        
        for chunk_df in self.chunk_reader(data_sheet_name, usecols):
            processed_chunk = self.process_chunk(chunk_df)
            
            if not processed_chunk.empty:
                result_chunks.append(processed_chunk)
                self.process_stats['total_rows_processed'] += len(processed_chunk)
                self.process_stats['chunks_processed'] += 1
                
                print(f"块处理完成，包含 {len(processed_chunk)} 行数据")
            
            # 清理内存
            del chunk_df, processed_chunk
            gc.collect()
        
        # 4. 合并所有结果
        print("正在合并所有处理结果...")
        if result_chunks:
            final_result = pd.concat(result_chunks, ignore_index=True)
            # 按日期排序
            final_result = final_result.sort_values('日期').reset_index(drop=True)
            
            self.process_stats['end_time'] = datetime.now()
            self.print_processing_stats()
            
            return final_result
        else:
            print("没有成功处理任何数据块")
            return pd.DataFrame()
    
    def print_processing_stats(self):
        """打印处理统计信息"""
        if self.process_stats['start_time'] and self.process_stats['end_time']:
            duration = self.process_stats['end_time'] - self.process_stats['start_time']
            print(f"\n=== 处理统计 ===")
            print(f"总处理时间: {duration}")
            print(f"处理的数据块数: {self.process_stats['chunks_processed']}")
            print(f"总处理行数: {self.process_stats['total_rows_processed']}")
            print(f"内存调整次数: {self.process_stats['memory_adjustments']}")
            print(f"最终chunk_size: {self.chunk_size}")
            print(f"当前内存使用: {self.get_memory_usage():.1f}MB")
            if duration.total_seconds() > 0:
                print(f"处理速度: {self.process_stats['total_rows_processed'] / duration.total_seconds():.1f} 行/秒")

def main():
    """主函数"""
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"错误：文件不存在 - {file_path}")
        return
    
    # 创建处理器实例
    processor = AdvancedChunkedExcelProcessor(
        file_path, 
        chunk_size=5000,  # 初始chunk_size
        max_memory_mb=300  # 最大内存限制
    )
    
    try:
        # 分块处理数据
        result = processor.process_in_chunks()
        
        if not result.empty:
            print("\n处理结果预览:")
            print(result.head(10))
            print(f"\n数据统计:")
            print(f"总行数: {len(result)}")
            print(f"日期范围: {result['日期'].min()} 到 {result['日期'].max()}")
            print(f"指标和统计: 最小值={result['指标和'].min():.2f}, 最大值={result['指标和'].max():.2f}, 平均值={result['指标和'].mean():.2f}")
            
            # 保存结果到文件
            output_path = os.path.join(os.path.dirname(__file__), 'processed_result_advanced.csv')
            result.to_csv(output_path, index=False, encoding='utf-8-sig')
            print(f"\n结果已保存到: {output_path}")
        else:
            print("处理失败，没有获得有效结果")
            
    except Exception as e:
        print(f"处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
