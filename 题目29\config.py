# 分块处理配置文件

# 文件路径配置
FILE_CONFIG = {
    'excel_filename': '原油期权日行情历史数据20250721.xlsx',
    'output_filename': 'processed_result.csv',
    'backup_enabled': True,  # 是否创建备份
}

# 分块处理配置
CHUNK_CONFIG = {
    'initial_chunk_size': 5000,      # 初始分块大小（行数）
    'min_chunk_size': 1000,          # 最小分块大小（行数）
    'max_memory_mb': 300,            # 最大内存使用量（MB）
    'memory_check_interval': 1,      # 内存检查间隔（每处理几个块检查一次）
    'auto_adjust_chunk_size': True,  # 是否自动调整分块大小
    'memory_reduction_factor': 0.7,  # 内存过高时的缩减因子
}

# 数据处理配置
DATA_CONFIG = {
    'demand_sheet_index': 0,                    # 需求表的sheet索引
    'data_sheet_name': '原油期权行情历史',        # 数据表的sheet名称
    'date_column': '日期',                      # 日期列名
    'volume_column': '日成交量(张)',             # 成交量列名（用于换算）
    'parse_dates': True,                        # 是否解析日期
    'sort_by_date': True,                       # 是否按日期排序
}

# 指标映射配置
INDICATOR_MAPPING = {
    '成交金额': '成交额（万）',
    '成交量': '成交量',
    '持仓量': '持仓量',
    '行权量': '行权量',
}

# 输出配置
OUTPUT_CONFIG = {
    'show_preview_rows': 10,         # 预览显示的行数
    'save_to_csv': True,             # 是否保存为CSV
    'csv_encoding': 'utf-8-sig',     # CSV编码
    'include_statistics': True,      # 是否显示统计信息
    'show_processing_stats': True,   # 是否显示处理统计
}

# 性能优化配置
PERFORMANCE_CONFIG = {
    'enable_gc': True,               # 是否启用垃圾回收
    'gc_interval': 5,                # 垃圾回收间隔（每处理几个块执行一次）
    'use_low_memory': True,          # 是否使用低内存模式
    'parallel_processing': False,    # 是否启用并行处理（实验性功能）
    'cache_indicators': True,        # 是否缓存指标信息
}

# 日志配置
LOG_CONFIG = {
    'enable_detailed_logging': True,  # 是否启用详细日志
    'show_memory_usage': True,        # 是否显示内存使用情况
    'show_progress': True,            # 是否显示进度
    'log_chunk_processing': True,     # 是否记录每个块的处理情况
}

# 错误处理配置
ERROR_CONFIG = {
    'continue_on_chunk_error': True,  # 单个块出错时是否继续处理
    'max_consecutive_errors': 3,      # 最大连续错误数
    'retry_failed_chunks': True,      # 是否重试失败的块
    'max_retries': 2,                 # 最大重试次数
}

# 验证配置的函数
def validate_config():
    """验证配置参数的合理性"""
    errors = []
    
    # 验证分块配置
    if CHUNK_CONFIG['initial_chunk_size'] < CHUNK_CONFIG['min_chunk_size']:
        errors.append("initial_chunk_size 不能小于 min_chunk_size")
    
    if CHUNK_CONFIG['max_memory_mb'] < 50:
        errors.append("max_memory_mb 不能小于 50MB")
    
    if not (0 < CHUNK_CONFIG['memory_reduction_factor'] < 1):
        errors.append("memory_reduction_factor 必须在 0 和 1 之间")
    
    # 验证输出配置
    if OUTPUT_CONFIG['show_preview_rows'] < 0:
        errors.append("show_preview_rows 不能为负数")
    
    # 验证错误处理配置
    if ERROR_CONFIG['max_consecutive_errors'] < 1:
        errors.append("max_consecutive_errors 必须大于 0")
    
    if ERROR_CONFIG['max_retries'] < 0:
        errors.append("max_retries 不能为负数")
    
    if errors:
        raise ValueError("配置验证失败:\n" + "\n".join(f"- {error}" for error in errors))
    
    return True

# 获取完整配置的函数
def get_config():
    """获取完整的配置字典"""
    validate_config()
    
    return {
        'file': FILE_CONFIG,
        'chunk': CHUNK_CONFIG,
        'data': DATA_CONFIG,
        'indicator_mapping': INDICATOR_MAPPING,
        'output': OUTPUT_CONFIG,
        'performance': PERFORMANCE_CONFIG,
        'log': LOG_CONFIG,
        'error': ERROR_CONFIG,
    }

# 打印配置信息的函数
def print_config():
    """打印当前配置信息"""
    config = get_config()
    
    print("=== 当前配置 ===")
    for section, settings in config.items():
        print(f"\n[{section.upper()}]")
        for key, value in settings.items():
            print(f"  {key}: {value}")

if __name__ == "__main__":
    print_config()
