# 原油期权数据分块处理工具

## 概述
这个工具用于处理大型Excel文件中的原油期权数据，通过分块机制避免内存溢出问题。

## 文件说明

### 1. `process.py` (原始版本)
- 原始的处理脚本，一次性加载所有数据
- 适用于小型数据集
- 可能在大数据量时出现内存问题

### 2. `process_chunked.py` (分块处理版本) ⭐ 推荐
- **主要改进**：添加了分块处理机制
- **内存优化**：避免一次性加载过大数据
- **错误处理**：单个块出错不影响整体处理
- **进度显示**：实时显示处理进度

### 3. `process_advanced_chunked.py` (高级版本)
- 包含内存监控和自适应分块大小调整
- 支持性能统计和详细日志
- 适用于对性能要求更高的场景

### 4. `process_final.py` + `config.py` (配置化版本)
- 使用配置文件管理所有参数
- 支持更多自定义选项
- 适用于需要频繁调整参数的场景

## 使用方法

### 快速开始（推荐）
```bash
python process_chunked.py
```

### 主要特性
1. **自动分块处理**：将大数据集分成小块处理，默认每块5000行
2. **内存友好**：处理完每个块后立即释放内存
3. **错误容错**：单个块出错不会影响整体处理
4. **进度显示**：实时显示处理进度和统计信息

### 处理流程
1. 读取需求表首行，获取指标列表
2. 读取完整数据并按日期排序
3. 将数据分成多个块进行处理
4. 对每个块计算指标和
5. 合并所有结果并保存

### 输出结果
- **控制台输出**：处理进度、统计信息、结果预览
- **CSV文件**：`processed_result.csv` - 包含日期和指标和的完整结果

## 配置选项

### 修改分块大小
在 `process_chunked.py` 中修改：
```python
processor = ChunkedExcelProcessor(file_path, chunk_size=5000)  # 调整这个数值
```

### 支持的指标
- 成交金额 → 成交额（万）
- 成交量 → 成交量  
- 持仓量 → 持仓量
- 行权量 → 行权量

## 性能优化建议

1. **调整分块大小**：
   - 内存充足：增大chunk_size (如10000)
   - 内存不足：减小chunk_size (如2000)

2. **系统资源**：
   - 确保有足够的可用内存
   - 关闭不必要的程序释放内存

3. **数据预处理**：
   - 如果只需要特定日期范围，可以先筛选数据

## 错误处理

### 常见问题
1. **内存不足**：减小chunk_size参数
2. **文件不存在**：检查Excel文件路径
3. **列名不匹配**：检查Excel文件中的列名是否正确

### 依赖安装
```bash
pip install pandas openpyxl
```

## 处理结果示例

```
正在读取需求指标...
找到指标: ['成交金额', '成交量', '持仓量', '行权量']
开始分块处理，chunk_size=5000
需要读取的列: ['日期', '成交额（万）', '成交量', '持仓量', '行权量']
正在读取完整数据...
数据读取完成，总行数: 128188
将分为 26 个块进行处理，每块最多 5000 行
正在处理第 1/26 块 (行 1 到 5000)...
第 1 块处理完成，包含 5000 行数据
...
处理完成！总共 128188 行数据

处理结果预览:
          日期     指标和
0 2021-06-21    0.00
1 2021-06-21  859.09
2 2021-06-21  437.58
...

数据统计:
总行数: 128188
日期范围: 2021-06-21 00:00:00 到 2025-07-18 00:00:00
指标和统计: 最小值=0.00, 最大值=116917.41, 平均值=842.98

结果已保存到: processed_result.csv
```

## 版本历史

- **v1.0** (`process.py`): 基础版本
- **v2.0** (`process_chunked.py`): 添加分块处理机制 ⭐
- **v3.0** (`process_advanced_chunked.py`): 添加内存监控和自适应调整
- **v4.0** (`process_final.py`): 添加配置文件支持

## 技术说明

### 分块策略
1. **数据读取**：一次性读取完整数据（相对安全的内存使用）
2. **分块处理**：将DataFrame按行索引分块
3. **内存管理**：处理完每个块后立即删除临时变量
4. **结果合并**：使用pandas.concat高效合并结果

### 内存优化
- 使用`del`显式删除大对象
- 分块处理避免同时处理所有数据
- 及时释放中间结果的内存

这个分块处理机制可以有效处理大型Excel文件，避免内存溢出问题，同时保持处理的准确性和完整性。
