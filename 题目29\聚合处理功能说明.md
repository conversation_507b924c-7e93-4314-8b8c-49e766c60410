# 按更新频率聚合处理功能说明（品种所有合约合计）

## 功能概述

在原有的分块处理基础上，新增了按更新频率聚合的处理函数 `aggregate_by_frequency()`，该函数会：
1. 只处理需求表中"数据样本"列为"品种所有合约合计"的数据
2. 根据"更新频率"列，将数据分为日度、周度平均和月度平均三种类型
3. 分别进行聚合处理并保存到三个不同的文件中
4. 使用简化的列名（直接使用分类名称）

## 数据筛选和指标集合

### 数据筛选
只处理需求表中"数据样本"为"品种所有合约合计"的规则，共9条规则

### 指标集合
系统会自动识别所有相关指标，创建完整的指标集合（共15个指标）：
- Delta, 前结算价, 开盘价, 成交量, 成交金额, 持仓量, 收盘价, 最低价, 最高价, 涨停价, 涨跌幅, 结算价, 行权量, 跌停价, 隐含波动率%

### 处理策略
- **基础处理**: 使用前4个基础指标（成交金额、成交量、持仓量、行权量）计算指标和
- **聚合处理**: 根据每个分类规则的具体指标要求进行相应的聚合计算

## 更新频率分类

根据需求表（demand_sheet = 0）中的[更新频率]列，系统会自动识别三种不同的更新频率：

1. **日度** (3个规则)
2. **周度平均** (3个规则)
3. **月度平均** (3个规则)

## 聚合规则

每种更新频率下，根据[分类]列进一步识别三种不同的聚合方式：

### 1. 日期连续合计值
- **操作方法**: 计算总数（求和）
- **适用指标**: 成交金额、成交量、持仓量、行权量等数量型指标
- **计算公式**: 同一日期内所有相关指标的总和

### 2. 日期连续加权平均值  
- **操作方法**: 计算加权平均值
- **权重**: 使用成交量作为权重（如果可用）
- **适用指标**: 涨跌幅、前结算价、开盘价、最高价、最低价、收盘价、结算价、涨停价、跌停价、隐含波动率%、Delta等价格型指标
- **计算公式**: Σ(指标值 × 成交量) / Σ(成交量)
- **备选方案**: 如果没有成交量数据，则使用算术平均值

### 3. 日期连续算数平均值
- **操作方法**: 计算算术平均值
- **适用指标**: 涨跌幅、前结算价、开盘价、最高价、最低价、收盘价、结算价、涨停价、跌停价、隐含波动率%、Delta等价格型指标
- **计算公式**: Σ(指标值) / 数据条数

## 处理流程

1. **基础处理**: 首先执行原有的分块处理，计算每行的指标和
2. **读取分类规则**: 从需求表中读取所有分类规则、更新频率和对应的指标
3. **按更新频率分组**: 将分类规则按更新频率分为三组
4. **重新读取数据**: 读取聚合所需的所有列数据
5. **分别处理三种频率**:
   - **日度**: 按日期分组聚合
   - **周度平均**: 按周分组，先按日聚合再求周平均
   - **月度平均**: 按月分组，先按日聚合再求月平均
6. **生成三个结果文件**: 分别保存到不同的CSV文件

## 输出文件

### 1. basic_processed_result.csv
- **内容**: 原有的基础处理结果
- **格式**: 日期 + 指标和
- **行数**: 与原始数据行数相同（128,188行）

### 2. 日度聚合结果.csv
- **内容**: 日度更新频率的聚合结果
- **格式**: 日期 + 3个聚合指标列
- **行数**: 990行（按日期去重）
- **列说明**:
  - `日期连续合计值`: 成交金额的合计值
  - `日期连续加权平均值`: 涨跌幅的加权平均值
  - `日期连续算数平均值`: 涨跌幅的算术平均值

### 3. 周度平均聚合结果.csv
- **内容**: 周度平均更新频率的聚合结果
- **格式**: 年周 + 开始日期 + 结束日期 + 3个聚合指标列
- **行数**: 208行（按周去重）
- **特殊处理**: 先按日聚合，再计算每周的平均值
- **列说明**:
  - `年周`: 格式为"YYYY-MM-DD/YYYY-MM-DD"
  - `开始日期`、`结束日期`: 该周的起止日期
  - `日期连续合计值`: 该周内成交金额的平均值
  - `日期连续加权平均值`: 该周内涨跌幅加权平均值的平均值
  - `日期连续算数平均值`: 该周内涨跌幅算术平均值的平均值

### 4. 月度平均聚合结果.csv
- **内容**: 月度平均更新频率的聚合结果
- **格式**: 年月 + 开始日期 + 结束日期 + 3个聚合指标列
- **行数**: 50行（按月去重）
- **特殊处理**: 先按日聚合，再计算每月的平均值
- **列说明**:
  - `年月`: 格式为"YYYY-MM"
  - `开始日期`、`结束日期`: 该月的起止日期
  - `日期连续合计值`: 该月内成交金额的平均值
  - `日期连续加权平均值`: 该月内涨跌幅加权平均值的平均值
  - `日期连续算数平均值`: 该月内涨跌幅算术平均值的平均值

## 代码结构

### 新增函数

1. **read_demand_classifications()**: 读取需求表的分类信息和更新频率，只处理"品种所有合约合计"数据
2. **aggregate_by_frequency()**: 按更新频率分组执行聚合处理
3. **_aggregate_daily()**: 执行日度聚合处理
4. **_aggregate_weekly()**: 执行周度平均聚合处理
5. **_aggregate_monthly()**: 执行月度平均聚合处理
6. **_calculate_aggregation()**: 通用聚合计算函数

### 修改的函数

1. **read_demand_indicators()**: 修改为读取所有"品种所有合约合计"相关的指标，创建完整的指标集合（15个指标）
2. **process_chunk()**: 修改为处理所有指标列，为后续聚合提供完整数据
3. **process_in_chunks()**: 在原有处理基础上增加按频率聚合处理步骤
4. **main()**: 更新以处理新的返回值格式（基础结果 + 按频率聚合结果）

## 使用方法

```bash
python process_chunked.py
```

运行后会生成四个CSV文件：
- `basic_processed_result.csv`: 基础处理结果
- `日度聚合结果.csv`: 日度聚合处理结果
- `周度平均聚合结果.csv`: 周度平均聚合处理结果
- `月度平均聚合结果.csv`: 月度平均聚合处理结果

## 性能特点

- **内存优化**: 聚合处理时重新读取数据，避免长时间占用大量内存
- **错误容错**: 聚合处理失败时仍会返回基础处理结果
- **进度显示**: 实时显示聚合处理进度
- **自动识别**: 根据需求表自动识别所有分类规则，无需手动配置

## 示例结果

### 日度聚合结果
```
          日期  日期连续合计值_成交金额_成交量_持仓量_行权量  日期连续加权平均值_...  日期连续算数平均值_...
0 2021-06-21                           11195.75                    16.459007                   26.701595
1 2021-06-22                           14465.70                    16.681787                   26.736051
2 2021-06-23                           11082.66                    17.399979                   27.014965
```

### 周度平均聚合结果
```
                      年周        开始日期        结束日期  [指标]_周平均  ...
0  2021-06-21/2021-06-27  2021-06-21  2021-06-25      12964.75  ...
1  2021-06-28/2021-07-04  2021-06-28  2021-07-02      17512.14  ...
```

### 月度平均聚合结果
```
        年月        开始日期        结束日期  [指标]_月平均  ...
0  2021-06  2021-06-21  2021-06-30      13973.54  ...
1  2021-07  2021-07-01  2021-07-30      30890.77  ...
```

## 数据统计
- **日度聚合**: 990行数据，覆盖990个交易日
- **周度平均**: 208行数据，覆盖208个交易周
- **月度平均**: 50行数据，覆盖50个交易月

这个功能完全兼容原有的处理流程，在不影响基础功能的前提下，提供了更丰富的多时间维度数据聚合分析能力。
