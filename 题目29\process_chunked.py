import pandas as pd
import os
from typing import List, Dict, Optional
import numpy as np

file_path = os.path.join(os.path.dirname(__file__), '原油期权日行情历史数据20250721.xlsx')

class ChunkedExcelProcessor:
    """分块处理Excel数据的类"""
    
    def __init__(self, file_path: str, chunk_size: int = 10000):
        """
        初始化处理器
        
        Args:
            file_path: Excel文件路径
            chunk_size: 每个分块的行数，默认10000行
        """
        self.file_path = file_path
        self.chunk_size = chunk_size
        self.indicators = []
        self.col_map = {
            '涨跌幅': '涨跌幅（%）',
            '前结算价': '前结算价',
            '开盘价': '开盘价',
            '成交金额': '成交额（万）',
            '成交量': '成交量',
            '持仓量': '持仓量',
            '行权量': '行权量',
            '最高价': '最高价',
            '最低价': '最低价',
            '收盘价': '收盘价',
            '结算价': '结算价',
            '涨停价': '涨停价',
            '跌停价': '跌停价',
            '隐含波动率%': '隐含波动率%',
            'Delta': 'Delta'
        }
        
    def read_demand_indicators(self) -> List[str]:
        """读取需求表首行，获取指标列表"""
        print("正在读取需求指标...")
        demand_sheet = 0
        row0 = pd.read_excel(self.file_path, sheet_name=demand_sheet, nrows=1)

        # 收集"包含指标*"列
        indicators = []
        for c in row0.columns:
            if c.startswith('包含指标') and pd.notna(row0[c].iloc[0]):
                value = row0[c].iloc[0]
                if isinstance(value, str):
                    indicators.append(value.strip())
                else:
                    indicators.append(str(value).strip())

        self.indicators = indicators
        print(f"找到指标: {indicators}")
        return indicators

    def read_demand_classifications(self) -> pd.DataFrame:
        """读取需求表的分类信息，只处理'品种所有合约合计'的数据"""
        print("正在读取需求分类信息...")
        demand_sheet = 0
        demand_df = pd.read_excel(self.file_path, sheet_name=demand_sheet)

        # 只筛选"数据样本"为"品种所有合约合计"的数据
        filtered_df = demand_df[demand_df['数据样本'] == '品种所有合约合计']
        print(f"筛选出 {len(filtered_df)} 个'品种所有合约合计'的规则")

        # 提取分类信息
        classifications = []
        for _, row in filtered_df.iterrows():
            classification = row['分类']
            update_frequency = row['更新频率']
            data_sample = row['数据样本']
            indicators = []

            # 收集该行的所有指标
            for col in filtered_df.columns:
                if col.startswith('包含指标') and pd.notna(row[col]):
                    indicators.append(str(row[col]).strip())

            if indicators:  # 只有当有指标时才添加
                classifications.append({
                    '分类': classification,
                    '更新频率': update_frequency,
                    '数据样本': data_sample,
                    '指标': indicators
                })

        classification_df = pd.DataFrame(classifications)
        print(f"处理 {len(classification_df)} 个分类规则")

        # 按更新频率分组显示统计
        freq_counts = classification_df['更新频率'].value_counts()
        print("更新频率分布:")
        for freq, count in freq_counts.items():
            print(f"  {freq}: {count}个规则")

        return classification_df
    
    def get_total_rows(self, sheet_name: str) -> int:
        """获取指定sheet的总行数"""
        print(f"正在计算 {sheet_name} 的总行数...")
        # 读取第一列来计算总行数，避免加载所有数据
        temp_df = pd.read_excel(self.file_path, sheet_name=sheet_name, usecols=[0])
        total_rows = len(temp_df)
        print(f"总行数: {total_rows}")
        return total_rows
    
    def process_chunk(self, chunk_df: pd.DataFrame) -> pd.DataFrame:
        """处理单个数据块"""
        # ── 直接使用指标对应的列进行求和 ──
        sum_cols = [self.col_map[i] for i in self.indicators]

        # ── 逐日求和 ──
        chunk_df['指标和'] = chunk_df[sum_cols].sum(axis=1)

        # 返回处理后的结果
        return chunk_df[['日期', '指标和']]

    def aggregate_by_frequency(self, df: pd.DataFrame) -> dict:
        """根据更新频率分组进行聚合处理"""
        print("正在进行按更新频率的聚合处理...")

        # 读取分类规则
        classification_df = self.read_demand_classifications()

        # 按更新频率分组
        frequency_groups = classification_df.groupby('更新频率')
        results = {}

        for frequency, freq_rules in frequency_groups:
            print(f"\n处理 {frequency} 数据...")

            if frequency == '日度':
                # 日度数据：按日期聚合
                aggregated_data = self._aggregate_daily(df, freq_rules)
            elif frequency == '周度平均':
                # 周度数据：按周聚合并求平均
                aggregated_data = self._aggregate_weekly(df, freq_rules)
            elif frequency == '月度平均':
                # 月度数据：按月聚合并求平均
                aggregated_data = self._aggregate_monthly(df, freq_rules)
            else:
                print(f"未知的更新频率: {frequency}")
                continue

            results[frequency] = aggregated_data
            print(f"{frequency} 处理完成，共 {len(aggregated_data)} 行数据")

        return results

    def _aggregate_daily(self, df: pd.DataFrame, rules: pd.DataFrame) -> pd.DataFrame:
        """日度聚合处理"""
        aggregated_results = []
        grouped = df.groupby('日期')

        for date, date_group in grouped:
            date_results = {'日期': date}

            for _, rule in rules.iterrows():
                classification = rule['分类']
                indicators = rule['指标']

                data_cols = [self.col_map[ind] for ind in indicators if ind in self.col_map]
                if not data_cols:
                    continue

                result_value = self._calculate_aggregation(date_group, data_cols, classification)
                # 直接使用分类名称作为列名
                date_results[classification] = result_value

            aggregated_results.append(date_results)

        return pd.DataFrame(aggregated_results)

    def _aggregate_weekly(self, df: pd.DataFrame, rules: pd.DataFrame) -> pd.DataFrame:
        """周度聚合处理"""
        # 添加周信息
        df_copy = df.copy()
        df_copy['年周'] = df_copy['日期'].dt.to_period('W')

        aggregated_results = []
        grouped = df_copy.groupby('年周')

        for week, week_group in grouped:
            week_results = {
                '年周': str(week),
                '开始日期': week_group['日期'].min(),
                '结束日期': week_group['日期'].max()
            }

            for _, rule in rules.iterrows():
                classification = rule['分类']
                indicators = rule['指标']

                data_cols = [self.col_map[ind] for ind in indicators if ind in self.col_map]
                if not data_cols:
                    continue

                # 周度平均：先按日聚合，再求周平均
                daily_values = []
                daily_grouped = week_group.groupby('日期')

                for _, day_group in daily_grouped:
                    daily_value = self._calculate_aggregation(day_group, data_cols, classification)
                    if pd.notna(daily_value) and daily_value != 0:
                        daily_values.append(daily_value)

                # 计算周平均
                week_avg = np.mean(daily_values) if daily_values else 0
                # 直接使用分类名称作为列名
                week_results[classification] = week_avg

            aggregated_results.append(week_results)

        return pd.DataFrame(aggregated_results)

    def _aggregate_monthly(self, df: pd.DataFrame, rules: pd.DataFrame) -> pd.DataFrame:
        """月度聚合处理"""
        # 添加月信息
        df_copy = df.copy()
        df_copy['年月'] = df_copy['日期'].dt.to_period('M')

        aggregated_results = []
        grouped = df_copy.groupby('年月')

        for month, month_group in grouped:
            month_results = {
                '年月': str(month),
                '开始日期': month_group['日期'].min(),
                '结束日期': month_group['日期'].max()
            }

            for _, rule in rules.iterrows():
                classification = rule['分类']
                indicators = rule['指标']

                data_cols = [self.col_map[ind] for ind in indicators if ind in self.col_map]
                if not data_cols:
                    continue

                # 月度平均：先按日聚合，再求月平均
                daily_values = []
                daily_grouped = month_group.groupby('日期')

                for _, day_group in daily_grouped:
                    daily_value = self._calculate_aggregation(day_group, data_cols, classification)
                    if pd.notna(daily_value) and daily_value != 0:
                        daily_values.append(daily_value)

                # 计算月平均
                month_avg = np.mean(daily_values) if daily_values else 0
                # 直接使用分类名称作为列名
                month_results[classification] = month_avg

            aggregated_results.append(month_results)

        return pd.DataFrame(aggregated_results)

    def _calculate_aggregation(self, group_data: pd.DataFrame, data_cols: list, classification: str) -> float:
        """计算聚合值"""
        if classification == '日期连续合计值':
            return group_data[data_cols].sum().sum()

        elif classification == '日期连续加权平均值':
            if '成交量' in self.col_map and self.col_map['成交量'] in group_data.columns:
                weights = group_data[self.col_map['成交量']]
                weighted_sum = 0
                total_weight = 0

                for col in data_cols:
                    if col in group_data.columns:
                        valid_mask = pd.notna(group_data[col]) & pd.notna(weights) & (weights > 0)
                        if valid_mask.any():
                            weighted_sum += (group_data[col][valid_mask] * weights[valid_mask]).sum()
                            total_weight += weights[valid_mask].sum()

                return weighted_sum / total_weight if total_weight > 0 else 0
            else:
                return group_data[data_cols].mean().mean()

        elif classification == '日期连续算数平均值':
            return group_data[data_cols].mean().mean()

        return 0
    
    def process_in_chunks(self) -> pd.DataFrame:
        """分块处理Excel数据"""
        # 1. 读取需求指标
        self.read_demand_indicators()

        # 2. 准备读取参数
        data_sheet_name = '原油期权行情历史'  # 第 3 个 sheet 的名称
        usecols = ['日期'] + [self.col_map[i] for i in self.indicators]

        print(f"开始分块处理，chunk_size={self.chunk_size}")
        print(f"需要读取的列: {usecols}")

        # 3. 先读取完整数据，然后分块处理（更简单可靠的方法）
        print("正在读取完整数据...")
        try:
            # 读取完整数据
            df = pd.read_excel(
                self.file_path,
                sheet_name=data_sheet_name,
                usecols=usecols,
                parse_dates=['日期']
            )

            print(f"数据读取完成，总行数: {len(df)}")

            # 保持表格原始顺序，不进行排序

            # 4. 分块处理数据
            total_rows = len(df)
            num_chunks = (total_rows + self.chunk_size - 1) // self.chunk_size
            print(f"将分为 {num_chunks} 个块进行处理，每块最多 {self.chunk_size} 行")

            result_chunks = []

            for i in range(num_chunks):
                start_idx = i * self.chunk_size
                end_idx = min(start_idx + self.chunk_size, total_rows)

                print(f"正在处理第 {i+1}/{num_chunks} 块 (行 {start_idx+1} 到 {end_idx})...")

                try:
                    # 获取当前块
                    chunk_df = df.iloc[start_idx:end_idx].copy()

                    # 处理当前块
                    processed_chunk = self.process_chunk(chunk_df)
                    result_chunks.append(processed_chunk)

                    print(f"第 {i+1} 块处理完成，包含 {len(processed_chunk)} 行数据")

                    # 清理内存
                    del chunk_df, processed_chunk

                except Exception as e:
                    print(f"处理第 {i+1} 块时出错: {e}")
                    continue

            # 清理原始数据
            del df

            # 5. 合并所有结果
            print("正在合并所有处理结果...")
            if result_chunks:
                final_result = pd.concat(result_chunks, ignore_index=True)
                print(f"初步处理完成！总共 {len(final_result)} 行数据")

                # 6. 进行按更新频率的聚合处理
                print("\n开始进行按更新频率的聚合处理...")

                # 重新读取完整数据用于聚合（需要所有列）
                try:
                    # 读取所有需要的列（包括用于聚合的列）
                    all_indicators = set()
                    classification_df = self.read_demand_classifications()

                    for _, row in classification_df.iterrows():
                        all_indicators.update(row['指标'])

                    # 构建需要读取的所有列
                    all_cols = ['日期']
                    for indicator in all_indicators:
                        if indicator in self.col_map:
                            all_cols.append(self.col_map[indicator])

                    # 去重
                    all_cols = list(set(all_cols))

                    print(f"重新读取数据用于聚合，需要的列: {all_cols}")

                    # 重新读取数据
                    full_df = pd.read_excel(
                        self.file_path,
                        sheet_name=data_sheet_name,
                        usecols=all_cols,
                        parse_dates=['日期']
                    )

                    # 进行按频率聚合处理
                    frequency_results = self.aggregate_by_frequency(full_df)

                    # 清理内存
                    del full_df

                    print(f"按频率聚合处理完成！")
                    return final_result, frequency_results

                except Exception as e:
                    print(f"聚合处理时出错: {e}")
                    print("返回基础处理结果")
                    return final_result, {}

            else:
                print("没有成功处理任何数据块")
                return pd.DataFrame(), pd.DataFrame()

        except Exception as e:
            print(f"读取数据时出错: {e}")
            return pd.DataFrame()

def main():
    """主函数"""
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"错误：文件不存在 - {file_path}")
        return
    
    # 创建处理器实例
    processor = ChunkedExcelProcessor(file_path, chunk_size=5000)
    
    try:
        # 分块处理数据
        result = processor.process_in_chunks()

        # 检查返回值类型
        if isinstance(result, tuple):
            basic_result, frequency_results = result
        else:
            basic_result = result
            frequency_results = {}

        if not basic_result.empty:
            print("\n=== 基础处理结果预览 ===")
            print(basic_result.head(10))
            print(f"\n基础数据统计:")
            print(f"总行数: {len(basic_result)}")
            print(f"日期范围: {basic_result['日期'].min()} 到 {basic_result['日期'].max()}")
            print(f"指标和统计: 最小值={basic_result['指标和'].min():.2f}, 最大值={basic_result['指标和'].max():.2f}, 平均值={basic_result['指标和'].mean():.2f}")

            # 保存基础结果
            basic_output_path = os.path.join(os.path.dirname(__file__), 'basic_processed_result.csv')
            basic_result.to_csv(basic_output_path, index=False, encoding='utf-8-sig')
            print(f"\n基础结果已保存到: {basic_output_path}")

            # 处理按频率聚合的结果
            if frequency_results:
                print("\n=== 按更新频率聚合结果 ===")

                # 保存日度数据
                if '日度' in frequency_results:
                    daily_data = frequency_results['日度']
                    daily_path = os.path.join(os.path.dirname(__file__), '日度聚合结果.csv')
                    daily_data.to_csv(daily_path, index=False, encoding='utf-8-sig')
                    print(f"日度聚合结果已保存到: {daily_path}")
                    print(f"  - 行数: {len(daily_data)}")
                    print(f"  - 列数: {len(daily_data.columns)}")
                    print(f"  - 预览:")
                    print(daily_data.head(3))

                # 保存周度平均数据
                if '周度平均' in frequency_results:
                    weekly_data = frequency_results['周度平均']
                    weekly_path = os.path.join(os.path.dirname(__file__), '周度平均聚合结果.csv')
                    weekly_data.to_csv(weekly_path, index=False, encoding='utf-8-sig')
                    print(f"\n周度平均聚合结果已保存到: {weekly_path}")
                    print(f"  - 行数: {len(weekly_data)}")
                    print(f"  - 列数: {len(weekly_data.columns)}")
                    print(f"  - 预览:")
                    print(weekly_data.head(3))

                # 保存月度平均数据
                if '月度平均' in frequency_results:
                    monthly_data = frequency_results['月度平均']
                    monthly_path = os.path.join(os.path.dirname(__file__), '月度平均聚合结果.csv')
                    monthly_data.to_csv(monthly_path, index=False, encoding='utf-8-sig')
                    print(f"\n月度平均聚合结果已保存到: {monthly_path}")
                    print(f"  - 行数: {len(monthly_data)}")
                    print(f"  - 列数: {len(monthly_data.columns)}")
                    print(f"  - 预览:")
                    print(monthly_data.head(3))

            else:
                print("\n按频率聚合处理未产生结果")

        else:
            print("处理失败，没有获得有效结果")
            
    except Exception as e:
        print(f"处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
