import pandas as pd
import os
from typing import List, Dict, Optional
import numpy as np

file_path = os.path.join(os.path.dirname(__file__), '原油期权日行情历史数据20250721.xlsx')

class ChunkedExcelProcessor:
    """分块处理Excel数据的类"""
    
    def __init__(self, file_path: str, chunk_size: int = 10000):
        """
        初始化处理器
        
        Args:
            file_path: Excel文件路径
            chunk_size: 每个分块的行数，默认10000行
        """
        self.file_path = file_path
        self.chunk_size = chunk_size
        self.indicators = []
        self.col_map = {
            '成交金额': '成交额（万）',
            '成交量': '成交量',
            '持仓量': '持仓量',
            '行权量': '行权量',
            '最低价': '最低价',
            '收盘价': '收盘价',
            '结算价': '结算价',
            '涨停价': '涨停价',
            '跌停价': '跌停价',
            '隐含波动率%': '隐含波动率%',
            'Delta': 'Delta', 
        }
        
    def read_demand_indicators(self) -> List[str]:
        """读取需求表首行，获取指标列表"""
        print("正在读取需求指标...")
        demand_sheet = 0
        row0 = pd.read_excel(self.file_path, sheet_name=demand_sheet, nrows=1)

        # 收集"包含指标*"列
        indicators = []
        for c in row0.columns:
            if c.startswith('包含指标') and pd.notna(row0[c].iloc[0]):
                value = row0[c].iloc[0]
                if isinstance(value, str):
                    indicators.append(value.strip())
                else:
                    indicators.append(str(value).strip())

        self.indicators = indicators
        print(f"找到指标: {indicators}")
        return indicators

    def read_demand_classifications(self) -> pd.DataFrame:
        """读取需求表的分类信息"""
        print("正在读取需求分类信息...")
        demand_sheet = 0
        demand_df = pd.read_excel(self.file_path, sheet_name=demand_sheet)

        # 提取分类信息
        classifications = []
        for _, row in demand_df.iterrows():
            classification = row['分类']
            indicators = []

            # 收集该行的所有指标
            for col in demand_df.columns:
                if col.startswith('包含指标') and pd.notna(row[col]):
                    indicators.append(str(row[col]).strip())

            if indicators:  # 只有当有指标时才添加
                classifications.append({
                    '分类': classification,
                    '指标': indicators
                })

        classification_df = pd.DataFrame(classifications)
        print(f"找到 {len(classification_df)} 个分类规则")
        return classification_df
    
    def get_total_rows(self, sheet_name: str) -> int:
        """获取指定sheet的总行数"""
        print(f"正在计算 {sheet_name} 的总行数...")
        # 读取第一列来计算总行数，避免加载所有数据
        temp_df = pd.read_excel(self.file_path, sheet_name=sheet_name, usecols=[0])
        total_rows = len(temp_df)
        print(f"总行数: {total_rows}")
        return total_rows
    
    def process_chunk(self, chunk_df: pd.DataFrame) -> pd.DataFrame:
        """处理单个数据块"""
        # ── 直接使用指标对应的列进行求和 ──
        sum_cols = [self.col_map[i] for i in self.indicators]

        # ── 逐日求和 ──
        chunk_df['指标和'] = chunk_df[sum_cols].sum(axis=1)

        # 返回处理后的结果
        return chunk_df[['日期', '指标和']]

    def aggregate_by_date_and_classification(self, df: pd.DataFrame) -> pd.DataFrame:
        """根据日期和分类规则进行聚合处理"""
        print("正在进行日期聚合处理...")

        # 读取分类规则
        classification_df = self.read_demand_classifications()

        # 准备结果列表
        aggregated_results = []

        # 按日期分组
        grouped = df.groupby('日期')

        for date, date_group in grouped:
            date_results = {'日期': date}

            # 对每个分类规则进行处理
            for _, class_rule in classification_df.iterrows():
                classification = class_rule['分类']
                indicators = class_rule['指标']

                # 获取该分类对应的数据列
                data_cols = []
                for indicator in indicators:
                    if indicator in self.col_map:
                        data_cols.append(self.col_map[indicator])

                if not data_cols:
                    continue

                # 根据分类类型进行不同的聚合操作
                if classification == '日期连续合计值':
                    # 计算总数（求和）
                    result_value = date_group[data_cols].sum().sum()
                    result_key = f"{classification}_{'_'.join(indicators)}"

                elif classification == '日期连续加权平均值':
                    # 计算加权平均值（这里使用成交量作为权重，如果有的话）
                    if '成交量' in self.col_map and self.col_map['成交量'] in date_group.columns:
                        weights = date_group[self.col_map['成交量']]
                        weighted_sum = 0
                        total_weight = 0

                        for col in data_cols:
                            if col in date_group.columns:
                                valid_mask = pd.notna(date_group[col]) & pd.notna(weights) & (weights > 0)
                                if valid_mask.any():
                                    weighted_sum += (date_group[col][valid_mask] * weights[valid_mask]).sum()
                                    total_weight += weights[valid_mask].sum()

                        result_value = weighted_sum / total_weight if total_weight > 0 else 0
                    else:
                        # 如果没有权重列，使用算术平均
                        result_value = date_group[data_cols].mean().mean()

                    result_key = f"{classification}_{'_'.join(indicators)}"

                elif classification == '日期连续算数平均值':
                    # 计算算术平均值
                    result_value = date_group[data_cols].mean().mean()
                    result_key = f"{classification}_{'_'.join(indicators)}"

                else:
                    continue

                date_results[result_key] = result_value

            aggregated_results.append(date_results)

        # 转换为DataFrame
        result_df = pd.DataFrame(aggregated_results)
        print(f"聚合处理完成，共处理 {len(result_df)} 个日期的数据")

        return result_df
    
    def process_in_chunks(self) -> pd.DataFrame:
        """分块处理Excel数据"""
        # 1. 读取需求指标
        self.read_demand_indicators()

        # 2. 准备读取参数
        data_sheet_name = '原油期权行情历史'  # 第 3 个 sheet 的名称
        usecols = ['日期'] + [self.col_map[i] for i in self.indicators]

        print(f"开始分块处理，chunk_size={self.chunk_size}")
        print(f"需要读取的列: {usecols}")

        # 3. 先读取完整数据，然后分块处理（更简单可靠的方法）
        print("正在读取完整数据...")
        try:
            # 读取完整数据
            df = pd.read_excel(
                self.file_path,
                sheet_name=data_sheet_name,
                usecols=usecols,
                parse_dates=['日期']
            )

            print(f"数据读取完成，总行数: {len(df)}")

            # 保持表格原始顺序，不进行排序

            # 4. 分块处理数据
            total_rows = len(df)
            num_chunks = (total_rows + self.chunk_size - 1) // self.chunk_size
            print(f"将分为 {num_chunks} 个块进行处理，每块最多 {self.chunk_size} 行")

            result_chunks = []

            for i in range(num_chunks):
                start_idx = i * self.chunk_size
                end_idx = min(start_idx + self.chunk_size, total_rows)

                print(f"正在处理第 {i+1}/{num_chunks} 块 (行 {start_idx+1} 到 {end_idx})...")

                try:
                    # 获取当前块
                    chunk_df = df.iloc[start_idx:end_idx].copy()

                    # 处理当前块
                    processed_chunk = self.process_chunk(chunk_df)
                    result_chunks.append(processed_chunk)

                    print(f"第 {i+1} 块处理完成，包含 {len(processed_chunk)} 行数据")

                    # 清理内存
                    del chunk_df, processed_chunk

                except Exception as e:
                    print(f"处理第 {i+1} 块时出错: {e}")
                    continue

            # 清理原始数据
            del df

            # 5. 合并所有结果
            print("正在合并所有处理结果...")
            if result_chunks:
                final_result = pd.concat(result_chunks, ignore_index=True)
                print(f"初步处理完成！总共 {len(final_result)} 行数据")

                # 6. 进行日期聚合处理
                print("\n开始进行日期聚合处理...")

                # 重新读取完整数据用于聚合（需要所有列）
                try:
                    # 读取所有需要的列（包括用于聚合的列）
                    all_indicators = set()
                    classification_df = self.read_demand_classifications()

                    for _, row in classification_df.iterrows():
                        all_indicators.update(row['指标'])

                    # 构建需要读取的所有列
                    all_cols = ['日期']
                    for indicator in all_indicators:
                        if indicator in self.col_map:
                            all_cols.append(self.col_map[indicator])

                    # 去重
                    all_cols = list(set(all_cols))

                    print(f"重新读取数据用于聚合，需要的列: {all_cols}")

                    # 重新读取数据
                    full_df = pd.read_excel(
                        self.file_path,
                        sheet_name=data_sheet_name,
                        usecols=all_cols,
                        parse_dates=['日期']
                    )

                    # 进行聚合处理
                    aggregated_result = self.aggregate_by_date_and_classification(full_df)

                    # 清理内存
                    del full_df

                    print(f"聚合处理完成！")
                    return final_result, aggregated_result

                except Exception as e:
                    print(f"聚合处理时出错: {e}")
                    print("返回基础处理结果")
                    return final_result, pd.DataFrame()

            else:
                print("没有成功处理任何数据块")
                return pd.DataFrame(), pd.DataFrame()

        except Exception as e:
            print(f"读取数据时出错: {e}")
            return pd.DataFrame()

def main():
    """主函数"""
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"错误：文件不存在 - {file_path}")
        return
    
    # 创建处理器实例
    processor = ChunkedExcelProcessor(file_path, chunk_size=5000)
    
    try:
        # 分块处理数据
        result = processor.process_in_chunks()

        # 检查返回值类型
        if isinstance(result, tuple):
            basic_result, aggregated_result = result
        else:
            basic_result = result
            aggregated_result = pd.DataFrame()

        if not basic_result.empty:
            print("\n=== 基础处理结果预览 ===")
            print(basic_result.head(10))
            print(f"\n基础数据统计:")
            print(f"总行数: {len(basic_result)}")
            print(f"日期范围: {basic_result['日期'].min()} 到 {basic_result['日期'].max()}")
            print(f"指标和统计: 最小值={basic_result['指标和'].min():.2f}, 最大值={basic_result['指标和'].max():.2f}, 平均值={basic_result['指标和'].mean():.2f}")

            # 保存基础结果
            basic_output_path = os.path.join(os.path.dirname(__file__), 'basic_processed_result.csv')
            basic_result.to_csv(basic_output_path, index=False, encoding='utf-8-sig')
            print(f"\n基础结果已保存到: {basic_output_path}")

            # 处理聚合结果
            if not aggregated_result.empty:
                print("\n=== 聚合处理结果预览 ===")
                print(aggregated_result.head(10))
                print(f"\n聚合数据统计:")
                print(f"总行数: {len(aggregated_result)}")
                print(f"列数: {len(aggregated_result.columns)}")
                print(f"聚合结果列名: {list(aggregated_result.columns)}")

                # 保存聚合结果
                agg_output_path = os.path.join(os.path.dirname(__file__), 'aggregated_result.csv')
                aggregated_result.to_csv(agg_output_path, index=False, encoding='utf-8-sig')
                print(f"\n聚合结果已保存到: {agg_output_path}")
            else:
                print("\n聚合处理未产生结果")

        else:
            print("处理失败，没有获得有效结果")
            
    except Exception as e:
        print(f"处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
