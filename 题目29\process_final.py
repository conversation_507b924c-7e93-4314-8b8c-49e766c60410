import pandas as pd
import os
import gc
import psutil
import shutil
from typing import List, Dict, Optional, Generator
import numpy as np
from datetime import datetime
from config import get_config

class FinalChunkedExcelProcessor:
    """最终版本的分块处理Excel数据的类，使用配置文件"""
    
    def __init__(self, config_override: Dict = None):
        """
        初始化处理器
        
        Args:
            config_override: 覆盖默认配置的字典
        """
        self.config = get_config()
        if config_override:
            self._update_config(config_override)
        
        self.file_path = os.path.join(
            os.path.dirname(__file__), 
            self.config['file']['excel_filename']
        )
        
        self.chunk_size = self.config['chunk']['initial_chunk_size']
        self.indicators = []
        self.process_stats = {
            'total_rows_processed': 0,
            'chunks_processed': 0,
            'memory_adjustments': 0,
            'errors_encountered': 0,
            'consecutive_errors': 0,
            'start_time': None,
            'end_time': None
        }
        
    def _update_config(self, override: Dict):
        """递归更新配置"""
        def update_dict(base, override):
            for key, value in override.items():
                if isinstance(value, dict) and key in base:
                    update_dict(base[key], value)
                else:
                    base[key] = value
        
        update_dict(self.config, override)
    
    def get_memory_usage(self) -> float:
        """获取当前内存使用量（MB）"""
        process = psutil.Process(os.getpid())
        return process.memory_info().rss / 1024 / 1024
    
    def log(self, message: str, level: str = 'INFO'):
        """日志输出"""
        if self.config['log']['enable_detailed_logging']:
            timestamp = datetime.now().strftime('%H:%M:%S')
            memory_info = f" [内存: {self.get_memory_usage():.1f}MB]" if self.config['log']['show_memory_usage'] else ""
            print(f"[{timestamp}] {level}: {message}{memory_info}")
    
    def adjust_chunk_size_if_needed(self):
        """根据内存使用情况自动调整chunk_size"""
        if not self.config['chunk']['auto_adjust_chunk_size']:
            return
            
        current_memory = self.get_memory_usage()
        max_memory = self.config['chunk']['max_memory_mb']
        
        if current_memory > max_memory:
            old_chunk_size = self.chunk_size
            reduction_factor = self.config['chunk']['memory_reduction_factor']
            min_chunk_size = self.config['chunk']['min_chunk_size']
            
            self.chunk_size = max(min_chunk_size, int(self.chunk_size * reduction_factor))
            self.process_stats['memory_adjustments'] += 1
            
            self.log(f"内存使用过高 ({current_memory:.1f}MB > {max_memory}MB)，调整chunk_size: {old_chunk_size} -> {self.chunk_size}", 'WARNING')
            
            if self.config['performance']['enable_gc']:
                gc.collect()
    
    def create_backup(self):
        """创建原文件的备份"""
        if not self.config['file']['backup_enabled']:
            return
            
        backup_path = self.file_path + '.backup'
        if not os.path.exists(backup_path):
            try:
                shutil.copy2(self.file_path, backup_path)
                self.log(f"已创建备份文件: {backup_path}")
            except Exception as e:
                self.log(f"创建备份失败: {e}", 'WARNING')
    
    def read_demand_indicators(self) -> List[str]:
        """读取需求表首行，获取指标列表"""
        self.log("正在读取需求指标...")
        
        try:
            demand_sheet = self.config['data']['demand_sheet_index']
            row0 = pd.read_excel(self.file_path, sheet_name=demand_sheet, nrows=1)
            
            # 收集"包含指标*"列
            indicators = [
                row0[c].strip()
                for c in row0.columns if c.startswith('包含指标') and pd.notna(row0[c])
            ]
            
            self.indicators = indicators
            self.log(f"找到指标: {indicators}")
            
            if self.config['performance']['cache_indicators']:
                self._cached_indicators = indicators
                
            return indicators
            
        except Exception as e:
            self.log(f"读取需求指标失败: {e}", 'ERROR')
            raise
    
    def get_sheet_info(self, sheet_name: str) -> Dict:
        """获取sheet的详细信息"""
        self.log(f"正在分析 {sheet_name} 的结构...")
        
        try:
            # 读取少量数据来分析结构
            sample_df = pd.read_excel(self.file_path, sheet_name=sheet_name, nrows=100)
            
            # 获取总行数
            if self.config['performance']['use_low_memory']:
                temp_df = pd.read_excel(self.file_path, sheet_name=sheet_name, usecols=[0])
                total_rows = len(temp_df)
                del temp_df
            else:
                full_df = pd.read_excel(self.file_path, sheet_name=sheet_name)
                total_rows = len(full_df)
                del full_df
            
            if self.config['performance']['enable_gc']:
                gc.collect()
            
            info = {
                'total_rows': total_rows,
                'columns': list(sample_df.columns),
                'dtypes': sample_df.dtypes.to_dict(),
                'memory_per_row_estimate': sample_df.memory_usage(deep=True).sum() / len(sample_df)
            }
            
            self.log(f"Sheet信息: 总行数={total_rows}, 列数={len(sample_df.columns)}, 预估每行内存={info['memory_per_row_estimate']:.2f}字节")
            return info
            
        except Exception as e:
            self.log(f"获取sheet信息失败: {e}", 'ERROR')
            raise
    
    def chunk_reader(self, sheet_name: str, usecols: List[str]) -> Generator[pd.DataFrame, None, None]:
        """生成器：逐块读取Excel数据"""
        sheet_info = self.get_sheet_info(sheet_name)
        total_rows = sheet_info['total_rows']
        
        # 根据内存估算调整初始chunk_size
        estimated_memory_per_chunk = (sheet_info['memory_per_row_estimate'] * self.chunk_size) / (1024 * 1024)
        max_memory = self.config['chunk']['max_memory_mb']
        
        if estimated_memory_per_chunk > max_memory * 0.5:
            self.chunk_size = max(
                self.config['chunk']['min_chunk_size'], 
                int(self.chunk_size * 0.5)
            )
            self.log(f"根据内存预估调整chunk_size为: {self.chunk_size}")
        
        current_row = 0
        chunk_num = 0
        
        while current_row < total_rows:
            chunk_num += 1
            nrows = min(self.chunk_size, total_rows - current_row)
            
            if self.config['log']['show_progress']:
                progress = (current_row / total_rows) * 100
                self.log(f"读取第 {chunk_num} 块: 行 {current_row+1} 到 {current_row+nrows} ({progress:.1f}%)")
            
            try:
                parse_dates = [self.config['data']['date_column']] if self.config['data']['parse_dates'] else None
                
                chunk_df = pd.read_excel(
                    self.file_path,
                    sheet_name=sheet_name,
                    usecols=usecols,
                    skiprows=current_row if current_row > 0 else 0,
                    nrows=nrows,
                    parse_dates=parse_dates
                )
                
                if not chunk_df.empty:
                    yield chunk_df
                    self.process_stats['consecutive_errors'] = 0  # 重置连续错误计数
                    
                current_row += nrows
                
                # 定期检查内存和执行垃圾回收
                if chunk_num % self.config['chunk']['memory_check_interval'] == 0:
                    self.adjust_chunk_size_if_needed()
                
                if (self.config['performance']['enable_gc'] and 
                    chunk_num % self.config['performance']['gc_interval'] == 0):
                    gc.collect()
                
            except Exception as e:
                self.process_stats['errors_encountered'] += 1
                self.process_stats['consecutive_errors'] += 1
                
                self.log(f"读取第 {chunk_num} 块时出错: {e}", 'ERROR')
                
                # 检查是否超过最大连续错误数
                if (self.process_stats['consecutive_errors'] >= 
                    self.config['error']['max_consecutive_errors']):
                    self.log("连续错误过多，停止处理", 'ERROR')
                    break
                
                if self.config['error']['continue_on_chunk_error']:
                    current_row += nrows  # 跳过这个块继续处理
                    continue
                else:
                    raise
    
    def process_chunk(self, chunk_df: pd.DataFrame) -> pd.DataFrame:
        """处理单个数据块"""
        try:
            col_map = self.config['indicator_mapping']
            volume_col = self.config['data']['volume_column']
            
            # 如需：成交金额按"成交量(张) → 万"换算后再参与求和
            if '成交金额' in self.indicators:
                chunk_df['成交金额_调整'] = chunk_df[col_map['成交金额']] * (chunk_df[volume_col] / 1e4)
                sum_cols = ['成交金额_调整'] + [
                    col_map[i] for i in self.indicators if i != '成交金额'
                ]
            else:
                sum_cols = [col_map[i] for i in self.indicators]
            
            # 逐日求和
            chunk_df['指标和'] = chunk_df[sum_cols].sum(axis=1)
            
            # 返回处理后的结果
            date_col = self.config['data']['date_column']
            result = chunk_df[[date_col, '指标和']].copy()
            
            # 清理临时列
            if '成交金额_调整' in chunk_df.columns:
                chunk_df.drop('成交金额_调整', axis=1, inplace=True)
            
            return result
            
        except Exception as e:
            self.log(f"处理数据块时出错: {e}", 'ERROR')
            return pd.DataFrame()
    
    def process_in_chunks(self) -> pd.DataFrame:
        """分块处理Excel数据"""
        self.process_stats['start_time'] = datetime.now()
        
        try:
            # 创建备份
            self.create_backup()
            
            # 读取需求指标
            self.read_demand_indicators()
            
            # 准备读取参数
            data_sheet_name = self.config['data']['data_sheet_name']
            date_col = self.config['data']['date_column']
            volume_col = self.config['data']['volume_column']
            col_map = self.config['indicator_mapping']
            
            usecols = [date_col] + [col_map[i] for i in self.indicators] + [volume_col]
            
            self.log(f"开始分块处理，初始chunk_size={self.chunk_size}, 最大内存限制={self.config['chunk']['max_memory_mb']}MB")
            
            # 分块处理数据
            result_chunks = []
            
            for chunk_df in self.chunk_reader(data_sheet_name, usecols):
                processed_chunk = self.process_chunk(chunk_df)
                
                if not processed_chunk.empty:
                    result_chunks.append(processed_chunk)
                    self.process_stats['total_rows_processed'] += len(processed_chunk)
                    self.process_stats['chunks_processed'] += 1
                    
                    if self.config['log']['log_chunk_processing']:
                        self.log(f"块处理完成，包含 {len(processed_chunk)} 行数据")
                
                # 清理内存
                del chunk_df, processed_chunk
                
            # 合并所有结果
            self.log("正在合并所有处理结果...")
            if result_chunks:
                final_result = pd.concat(result_chunks, ignore_index=True)
                
                # 按日期排序
                if self.config['data']['sort_by_date']:
                    date_col = self.config['data']['date_column']
                    final_result = final_result.sort_values(date_col).reset_index(drop=True)
                
                self.process_stats['end_time'] = datetime.now()
                
                if self.config['output']['show_processing_stats']:
                    self.print_processing_stats()
                
                return final_result
            else:
                self.log("没有成功处理任何数据块", 'WARNING')
                return pd.DataFrame()
                
        except Exception as e:
            self.log(f"处理过程中发生严重错误: {e}", 'ERROR')
            raise
    
    def print_processing_stats(self):
        """打印处理统计信息"""
        if self.process_stats['start_time'] and self.process_stats['end_time']:
            duration = self.process_stats['end_time'] - self.process_stats['start_time']
            
            print(f"\n=== 处理统计 ===")
            print(f"总处理时间: {duration}")
            print(f"处理的数据块数: {self.process_stats['chunks_processed']}")
            print(f"总处理行数: {self.process_stats['total_rows_processed']}")
            print(f"内存调整次数: {self.process_stats['memory_adjustments']}")
            print(f"遇到的错误数: {self.process_stats['errors_encountered']}")
            print(f"最终chunk_size: {self.chunk_size}")
            print(f"当前内存使用: {self.get_memory_usage():.1f}MB")
            
            if duration.total_seconds() > 0:
                speed = self.process_stats['total_rows_processed'] / duration.total_seconds()
                print(f"处理速度: {speed:.1f} 行/秒")
    
    def save_result(self, result: pd.DataFrame) -> str:
        """保存处理结果"""
        if not self.config['output']['save_to_csv']:
            return ""
        
        output_path = os.path.join(
            os.path.dirname(__file__), 
            self.config['file']['output_filename']
        )
        
        try:
            result.to_csv(
                output_path, 
                index=False, 
                encoding=self.config['output']['csv_encoding']
            )
            self.log(f"结果已保存到: {output_path}")
            return output_path
        except Exception as e:
            self.log(f"保存结果失败: {e}", 'ERROR')
            return ""

def main():
    """主函数"""
    # 检查文件是否存在
    config = get_config()
    file_path = os.path.join(os.path.dirname(__file__), config['file']['excel_filename'])
    
    if not os.path.exists(file_path):
        print(f"错误：文件不存在 - {file_path}")
        return
    
    # 创建处理器实例
    processor = FinalChunkedExcelProcessor()
    
    try:
        # 分块处理数据
        result = processor.process_in_chunks()
        
        if not result.empty:
            # 显示结果预览
            if config['output']['show_preview_rows'] > 0:
                print("\n处理结果预览:")
                print(result.head(config['output']['show_preview_rows']))
            
            # 显示统计信息
            if config['output']['include_statistics']:
                print(f"\n数据统计:")
                print(f"总行数: {len(result)}")
                date_col = config['data']['date_column']
                print(f"日期范围: {result[date_col].min()} 到 {result[date_col].max()}")
                stats = result['指标和'].describe()
                print(f"指标和统计: 最小值={stats['min']:.2f}, 最大值={stats['max']:.2f}, 平均值={stats['mean']:.2f}")
            
            # 保存结果
            output_path = processor.save_result(result)
            if output_path:
                print(f"\n✅ 处理完成！结果已保存到: {output_path}")
        else:
            print("❌ 处理失败，没有获得有效结果")
            
    except Exception as e:
        print(f"❌ 处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
