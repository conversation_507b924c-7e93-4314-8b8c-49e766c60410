import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.colors import LinearSegmentedColormap
import seaborn as sns
from scipy import ndimage
from scipy.signal import convolve2d
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.family'] = 'SimHei'
plt.rcParams['axes.unicode_minus'] = False

# 创建多尺度CNN特征图可视化
def create_multiscale_cnn_visualization():
    fig = plt.figure(figsize=(20, 16))
    
    # 1. 原始输入图像 (模拟手写数字)
    ax1 = plt.subplot(4, 5, 1)
    np.random.seed(42)
    input_image = np.zeros((28, 28))
    # 创建一个类似数字"8"的图案
    input_image[8:20, 10:18] = 1.0
    input_image[10:12, 12:16] = 0.0
    input_image[16:18, 12:16] = 0.0
    input_image = ndimage.gaussian_filter(input_image, sigma=1.0)
    
    im1 = ax1.imshow(input_image, cmap='viridis', aspect='equal')
    ax1.set_title('输入图像 (28×28)', fontsize=12, fontweight='bold')
    ax1.set_xticks([])
    ax1.set_yticks([])
    plt.colorbar(im1, ax=ax1, fraction=0.046, pad=0.04)
    
    # 2. 第一层卷积核可视化 (3×3, 5×5, 7×7)
    kernels = {
        '3×3边缘检测': np.array([[-1, -1, -1], [0, 0, 0], [1, 1, 1]]),
        '5×5高斯模糊': np.array([[1, 4, 6, 4, 1], [4, 16, 24, 16, 4], [6, 24, 36, 24, 6], 
                              [4, 16, 24, 16, 4], [1, 4, 6, 4, 1]]) / 256,
        '7×7拉普拉斯': np.array([[0, 0, -1, -1, -1, 0, 0], [0, -1, -2, -3, -2, -1, 0],
                              [-1, -2, -3, -5, -3, -2, -1], [-1, -3, -5, 24, -5, -3, -1],
                              [-1, -2, -3, -5, -3, -2, -1], [0, -1, -2, -3, -2, -1, 0],
                              [0, 0, -1, -1, -1, 0, 0]])
    }
    
    for i, (name, kernel) in enumerate(kernels.items()):
        ax = plt.subplot(4, 5, 2 + i)
        im = ax.imshow(kernel, cmap='RdBu_r', aspect='equal')
        ax.set_title(f'{name}卷积核', fontsize=10)
        ax.set_xticks([])
        ax.set_yticks([])
        plt.colorbar(im, ax=ax, fraction=0.046, pad=0.04)
    
    # 3. 特征图输出 (卷积后的结果)
    feature_maps = []
    for i, (name, kernel) in enumerate(kernels.items()):
        if kernel.shape[0] <= input_image.shape[0]:
            feature_map = convolve2d(input_image, kernel, mode='same', boundary='symm')
            feature_maps.append((name, feature_map))
    
    for i, (name, fmap) in enumerate(feature_maps):
        ax = plt.subplot(4, 5, 6 + i)
        im = ax.imshow(fmap, cmap='plasma', aspect='equal')
        ax.set_title(f'{name.split("×")[0]}×{name.split("×")[1].split("边")[0]}特征图', fontsize=10)
        ax.set_xticks([])
        ax.set_yticks([])
        plt.colorbar(im, ax=ax, fraction=0.046, pad=0.04)
    
    # 4. 池化层可视化 (Max Pooling vs Average Pooling)
    def max_pooling(image, pool_size=2):
        h, w = image.shape
        pooled = np.zeros((h//pool_size, w//pool_size))
        for i in range(0, h, pool_size):
            for j in range(0, w, pool_size):
                pooled[i//pool_size, j//pool_size] = np.max(image[i:i+pool_size, j:j+pool_size])
        return pooled
    
    def avg_pooling(image, pool_size=2):
        h, w = image.shape
        pooled = np.zeros((h//pool_size, w//pool_size))
        for i in range(0, h, pool_size):
            for j in range(0, w, pool_size):
                pooled[i//pool_size, j//pool_size] = np.mean(image[i:i+pool_size, j:j+pool_size])
        return pooled
    
    # 对第一个特征图进行池化
    if feature_maps:
        first_fmap = feature_maps[0][1]
        max_pooled = max_pooling(first_fmap, 2)
        avg_pooled = avg_pooling(first_fmap, 2)
        
        ax9 = plt.subplot(4, 5, 9)
        im9 = ax9.imshow(max_pooled, cmap='hot', aspect='equal')
        ax9.set_title('最大池化 (2×2)', fontsize=10)
        ax9.set_xticks([])
        ax9.set_yticks([])
        plt.colorbar(im9, ax=ax9, fraction=0.046, pad=0.04)
        
        ax10 = plt.subplot(4, 5, 10)
        im10 = ax10.imshow(avg_pooled, cmap='hot', aspect='equal')
        ax10.set_title('平均池化 (2×2)', fontsize=10)
        ax10.set_xticks([])
        ax10.set_yticks([])
        plt.colorbar(im10, ax=ax10, fraction=0.046, pad=0.04)
    
    # 5. 多尺度特征融合可视化
    ax11 = plt.subplot(4, 5, 11)
    # 创建多尺度特征融合的示意图
    fusion_data = np.random.rand(16, 16)
    for i in range(3):
        center_x, center_y = 8, 8
        radius = 3 + i * 2
        y, x = np.ogrid[:16, :16]
        mask = (x - center_x)**2 + (y - center_y)**2 <= radius**2
        fusion_data[mask] += (3 - i) * 0.5
    
    im11 = ax11.imshow(fusion_data, cmap='coolwarm', aspect='equal')
    ax11.set_title('多尺度特征融合', fontsize=10)
    ax11.set_xticks([])
    ax11.set_yticks([])
    plt.colorbar(im11, ax=ax11, fraction=0.046, pad=0.04)
    
    # 6. 注意力权重热力图
    ax12 = plt.subplot(4, 5, 12)
    attention_weights = np.random.rand(14, 14)
    # 添加一些结构化的注意力模式
    attention_weights[6:8, 6:8] = 0.9  # 中心高注意力
    attention_weights[2:4, 10:12] = 0.8  # 右上角
    attention_weights[10:12, 2:4] = 0.7  # 左下角
    attention_weights = ndimage.gaussian_filter(attention_weights, sigma=0.8)
    
    im12 = ax12.imshow(attention_weights, cmap='Reds', aspect='equal')
    ax12.set_title('注意力权重分布', fontsize=10)
    ax12.set_xticks([])
    ax12.set_yticks([])
    plt.colorbar(im12, ax=ax12, fraction=0.046, pad=0.04)
    
    # 7. 感受野可视化
    ax13 = plt.subplot(4, 5, 13)
    receptive_field = np.zeros((28, 28))
    # 模拟不同层的感受野大小
    rf_sizes = [3, 7, 15]
    colors = [0.3, 0.6, 0.9]
    center = 14
    
    for rf_size, color in zip(rf_sizes, colors):
        y, x = np.ogrid[:28, :28]
        mask = np.abs(x - center) + np.abs(y - center) <= rf_size // 2
        receptive_field[mask] = color
    
    im13 = ax13.imshow(receptive_field, cmap='Blues', aspect='equal')
    ax13.set_title('感受野演化', fontsize=10)
    ax13.set_xticks([])
    ax13.set_yticks([])
    plt.colorbar(im13, ax=ax13, fraction=0.046, pad=0.04)
    
    # 8. 特征激活统计分析
    ax14 = plt.subplot(4, 5, 14)
    if feature_maps:
        activations = [fmap[1].flatten() for fmap in feature_maps]
        ax14.hist(activations[0], bins=30, alpha=0.7, label='3×3特征', color='red')
        if len(activations) > 1:
            ax14.hist(activations[1], bins=30, alpha=0.7, label='5×5特征', color='blue')
        if len(activations) > 2:
            ax14.hist(activations[2], bins=30, alpha=0.7, label='7×7特征', color='green')
        ax14.set_title('特征激活分布', fontsize=10)
        ax14.set_xlabel('激活值')
        ax14.set_ylabel('频次')
        ax14.legend(fontsize=8)
        ax14.grid(True, alpha=0.3)
    
    # 9. 网络架构示意图
    ax15 = plt.subplot(4, 5, 15)
    ax15.set_xlim(0, 10)
    ax15.set_ylim(0, 8)
    
    # 绘制网络层
    layers = [
        {'name': '输入\n28×28', 'pos': (1, 4), 'size': (1.5, 1.5), 'color': 'lightblue'},
        {'name': '卷积1\n3×3,5×5,7×7', 'pos': (3.5, 4), 'size': (1.8, 1.5), 'color': 'lightgreen'},
        {'name': '池化\n2×2', 'pos': (6, 4), 'size': (1.2, 1.5), 'color': 'lightyellow'},
        {'name': '融合\n特征', 'pos': (8.5, 4), 'size': (1.2, 1.5), 'color': 'lightcoral'}
    ]
    
    for layer in layers:
        rect = patches.Rectangle((layer['pos'][0] - layer['size'][0]/2, 
                                layer['pos'][1] - layer['size'][1]/2),
                               layer['size'][0], layer['size'][1],
                               linewidth=2, edgecolor='black', 
                               facecolor=layer['color'], alpha=0.7)
        ax15.add_patch(rect)
        ax15.text(layer['pos'][0], layer['pos'][1], layer['name'], 
                 ha='center', va='center', fontsize=9, fontweight='bold')
    
    # 绘制连接线
    connections = [(1.75, 4, 2.6, 4), (4.4, 4, 5.4, 4), (6.6, 4, 7.9, 4)]
    for x1, y1, x2, y2 in connections:
        ax15.arrow(x1, y1, x2-x1, y2-y1, head_width=0.1, head_length=0.1, 
                  fc='black', ec='black')
    
    ax15.set_title('多尺度CNN架构', fontsize=10)
    ax15.set_xticks([])
    ax15.set_yticks([])
    ax15.axis('off')

    # 10. 性能指标对比
    ax16 = plt.subplot(4, 5, 16)
    metrics = ['准确率', '召回率', 'F1分数', '推理时间']
    single_scale = [0.85, 0.82, 0.83, 0.15]
    multi_scale = [0.92, 0.89, 0.90, 0.28]

    x = np.arange(len(metrics))
    width = 0.35

    bars1 = ax16.bar(x - width/2, single_scale, width, label='单尺度CNN', color='skyblue')
    bars2 = ax16.bar(x + width/2, multi_scale, width, label='多尺度CNN', color='orange')

    ax16.set_title('性能对比', fontsize=10)
    ax16.set_ylabel('数值')
    ax16.set_xticks(x)
    ax16.set_xticklabels(metrics, rotation=45, ha='right')
    ax16.legend(fontsize=8)
    ax16.grid(True, alpha=0.3)

    # 添加数值标签
    for bars in [bars1, bars2]:
        for bar in bars:
            height = bar.get_height()
            ax16.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                     f'{height:.2f}', ha='center', va='bottom', fontsize=8)

    # 11. 梯度流可视化
    ax17 = plt.subplot(4, 5, 17)
    # 创建梯度流的可视化
    gradient_flow = np.random.rand(20, 20) * 0.5
    # 添加一些梯度消失的模式
    for i in range(20):
        for j in range(20):
            distance_from_center = np.sqrt((i-10)**2 + (j-10)**2)
            gradient_flow[i, j] *= np.exp(-distance_from_center / 8)

    im17 = ax17.imshow(gradient_flow, cmap='viridis', aspect='equal')
    ax17.set_title('梯度流分析', fontsize=10)
    ax17.set_xticks([])
    ax17.set_yticks([])
    plt.colorbar(im17, ax=ax17, fraction=0.046, pad=0.04)

    # 12. 特征相似性矩阵
    ax18 = plt.subplot(4, 5, 18)
    if len(feature_maps) >= 3:
        similarity_matrix = np.zeros((3, 3))
        for i in range(3):
            for j in range(3):
                if i == j:
                    similarity_matrix[i, j] = 1.0
                else:
                    # 计算特征图之间的相关性
                    corr = np.corrcoef(feature_maps[i][1].flatten(),
                                     feature_maps[j][1].flatten())[0, 1]
                    similarity_matrix[i, j] = abs(corr) if not np.isnan(corr) else 0

        im18 = ax18.imshow(similarity_matrix, cmap='coolwarm', aspect='equal', vmin=-1, vmax=1)
        ax18.set_title('特征相似性矩阵', fontsize=10)
        ax18.set_xticks([0, 1, 2])
        ax18.set_yticks([0, 1, 2])
        ax18.set_xticklabels(['3×3', '5×5', '7×7'])
        ax18.set_yticklabels(['3×3', '5×5', '7×7'])
        plt.colorbar(im18, ax=ax18, fraction=0.046, pad=0.04)

        # 添加数值标签
        for i in range(3):
            for j in range(3):
                ax18.text(j, i, f'{similarity_matrix[i, j]:.2f}',
                         ha='center', va='center', fontsize=8,
                         color='white' if abs(similarity_matrix[i, j]) > 0.5 else 'black')

    # 13. 计算复杂度分析
    ax19 = plt.subplot(4, 5, 19)
    kernel_sizes = ['3×3', '5×5', '7×7', '多尺度']
    flops = [9, 25, 49, 83]  # 相对计算量
    memory = [12, 28, 52, 92]  # 相对内存使用

    x = np.arange(len(kernel_sizes))
    width = 0.35

    bars1 = ax19.bar(x - width/2, flops, width, label='计算量(FLOPs)', color='lightcoral')
    bars2 = ax19.bar(x + width/2, memory, width, label='内存使用', color='lightsteelblue')

    ax19.set_title('计算复杂度分析', fontsize=10)
    ax19.set_ylabel('相对数值')
    ax19.set_xticks(x)
    ax19.set_xticklabels(kernel_sizes)
    ax19.legend(fontsize=8)
    ax19.grid(True, alpha=0.3)

    # 14. 频域分析
    ax20 = plt.subplot(4, 5, 20)
    if feature_maps:
        # 对第一个特征图进行FFT分析
        fft_result = np.fft.fft2(feature_maps[0][1])
        fft_magnitude = np.log(np.abs(np.fft.fftshift(fft_result)) + 1)

        im20 = ax20.imshow(fft_magnitude, cmap='jet', aspect='equal')
        ax20.set_title('频域特征分析', fontsize=10)
        ax20.set_xticks([])
        ax20.set_yticks([])
        plt.colorbar(im20, ax=ax20, fraction=0.046, pad=0.04)

    plt.tight_layout(pad=2.0)
    plt.savefig('C:/Users/<USER>/OneDrive/Desktop/VLM prompt/VLM-code/code/题目28/multiscale_cnn_analysis.png',
                dpi=300, bbox_inches='tight', facecolor='white')
    plt.show()

if __name__ == "__main__":
    create_multiscale_cnn_visualization()
