# 选项D：多尺度CNN特征提取与融合实现（正确实现）
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt

class MultiScaleCNN(nn.Module):
    def __init__(self, input_channels=1, num_classes=10):
        super(MultiScaleCNN, self).__init__()
        
        # 多尺度卷积分支
        self.conv3x3 = nn.Conv2d(input_channels, 32, kernel_size=3, padding=1)
        self.conv5x5 = nn.Conv2d(input_channels, 32, kernel_size=5, padding=2)
        self.conv7x7 = nn.Conv2d(input_channels, 32, kernel_size=7, padding=3)
        
        # 批归一化层
        self.bn1 = nn.BatchNorm2d(96)  # 32*3=96个通道
        
        # 池化层
        self.maxpool = nn.MaxPool2d(kernel_size=2, stride=2)
        
        # 注意力机制
        self.attention = nn.Conv2d(96, 96, kernel_size=1)
        self.sigmoid = nn.Sigmoid()
        
        # 全连接层
        self.fc1 = nn.Linear(96 * 14 * 14, 128)
        self.dropout = nn.Dropout(0.5)
        self.fc2 = nn.Linear(128, num_classes)
        
    def forward(self, x):
        # 多尺度特征提取
        feat3x3 = F.relu(self.conv3x3(x))
        feat5x5 = F.relu(self.conv5x5(x))
        feat7x7 = F.relu(self.conv7x7(x))
        
        # 特征融合 - 正确使用拼接
        fused_features = torch.cat([feat3x3, feat5x5, feat7x7], dim=1)
        
        # 批归一化
        fused_features = self.bn1(fused_features)
        
        # 池化
        pooled = self.maxpool(fused_features)
        
        # 注意力权重计算
        attention_weights = self.sigmoid(self.attention(pooled))
        attended_features = pooled * attention_weights
        
        # 展平
        flattened = attended_features.view(attended_features.size(0), -1)
        
        # 全连接层
        x = F.relu(self.fc1(flattened))
        x = self.dropout(x)
        output = self.fc2(x)
        
        return output, attention_weights

# 训练函数
def train_multiscale_cnn():
    model = MultiScaleCNN()
    criterion = nn.CrossEntropyLoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    
    # 模拟训练数据
    batch_size = 32
    input_data = torch.randn(batch_size, 1, 28, 28)
    labels = torch.randint(0, 10, (batch_size,))
    
    model.train()
    optimizer.zero_grad()
    
    try:
        outputs, attention = model(input_data)
        loss = criterion(outputs, labels)
        loss.backward()
        optimizer.step()
        
        print(f"训练成功，损失值: {loss.item():.4f}")
        print(f"输出形状: {outputs.shape}")
        print(f"注意力权重形状: {attention.shape}")
        
    except Exception as e:
        print(f"训练过程中出现错误: {str(e)}")
        return False
    
    return True

# 模型架构分析
def analyze_model_architecture():
    """分析模型架构的合理性"""
    print("\n=== 模型架构分析 ===")
    
    model = MultiScaleCNN()
    
    # 计算参数数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"总参数数量: {total_params:,}")
    print(f"可训练参数数量: {trainable_params:,}")
    
    # 分析各层参数
    print("\n各层参数分布:")
    for name, param in model.named_parameters():
        print(f"  {name}: {param.numel():,} 参数")
    
    # 计算理论感受野
    receptive_fields = {
        '3×3卷积': 3,
        '5×5卷积': 5,
        '7×7卷积': 7,
        '池化后': 14  # 2×2池化使感受野翻倍
    }
    
    print(f"\n理论感受野大小:")
    for layer, rf_size in receptive_fields.items():
        print(f"  {layer}: {rf_size}×{rf_size}")

# 特征可视化函数
def visualize_features(model, input_data):
    model.eval()
    with torch.no_grad():
        # 获取中间特征
        feat3x3 = F.relu(model.conv3x3(input_data))
        feat5x5 = F.relu(model.conv5x5(input_data))
        feat7x7 = F.relu(model.conv7x7(input_data))
        
        # 可视化第一个样本的特征图
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        
        # 显示不同尺度的特征图
        for i, (feat, name) in enumerate([(feat3x3, '3×3'), (feat5x5, '5×5'), (feat7x7, '7×7')]):
            # 取第一个样本的第一个通道
            feature_map = feat[0, 0].cpu().numpy()
            axes[0, i].imshow(feature_map, cmap='viridis')
            axes[0, i].set_title(f'{name} 卷积特征图')
            axes[0, i].axis('off')
        
        # 显示特征统计
        for i, (feat, name) in enumerate([(feat3x3, '3×3'), (feat5x5, '5×5'), (feat7x7, '7×7')]):
            feature_flat = feat[0].cpu().numpy().flatten()
            axes[1, i].hist(feature_flat, bins=50, alpha=0.7)
            axes[1, i].set_title(f'{name} 特征分布')
            axes[1, i].set_xlabel('激活值')
            axes[1, i].set_ylabel('频次')
        
        plt.tight_layout()
        plt.savefig('C:/Users/<USER>/OneDrive/Desktop/VLM prompt/VLM-code/code/题目28/option_D_features.png')
        plt.show()

# 性能基准测试
def benchmark_performance():
    """性能基准测试"""
    print("\n=== 性能基准测试 ===")
    
    model = MultiScaleCNN()
    model.eval()
    
    # 测试不同批次大小的推理时间
    batch_sizes = [1, 8, 16, 32, 64]
    
    print("推理时间测试 (CPU):")
    for batch_size in batch_sizes:
        test_input = torch.randn(batch_size, 1, 28, 28)
        
        import time
        start_time = time.time()
        
        with torch.no_grad():
            for _ in range(10):  # 运行10次取平均
                outputs, attention = model(test_input)
        
        avg_time = (time.time() - start_time) / 10
        print(f"  批次大小 {batch_size}: {avg_time*1000:.2f} ms")

if __name__ == "__main__":
    print("选项D：多尺度CNN实现（正确版本）")
    success = train_multiscale_cnn()
    
    if success:
        # 创建模型进行特征可视化
        model = MultiScaleCNN()
        test_input = torch.randn(1, 1, 28, 28)
        visualize_features(model, test_input)
        
        # 模型架构分析
        analyze_model_architecture()
        
        # 性能基准测试
        benchmark_performance()
    
    print("选项D执行完成")
